@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 - 简化版本 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 修复水合不匹配：明确设置 overscroll 行为 */
  overscroll-behavior-x: none;
}

/* 矩阵专用样式 */

/* 矩阵视口（外层滚动容器） */
.matrix-viewport {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.matrix-viewport::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.matrix-viewport::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

.matrix-viewport::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

.matrix-viewport::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.matrix-viewport::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* 矩阵容器（内层矩阵） */
.matrix-container {
  will-change: transform;
  contain: layout style paint;
}

/* 矩阵单元格 */
.matrix-cell {
  transition: all 0.1s ease;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
}

.matrix-cell:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.matrix-cell.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.matrix-cell.coordinate-mode {
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  /* 字体大小由JS动态设置，不在CSS中固定 */
  letter-spacing: -0.5px;
  /* 紧凑显示 */
}

.matrix-cell.color-mode {
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
  /* 字体大小由JS动态设置 */
}

.matrix-cell.level-mode {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  /* 字体大小由JS动态设置 */
}

.matrix-cell.word-mode {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.2;
  /* 字体大小由JS动态设置，支持中文优化 */
}

/* 悬停时显示完整内容 */
.matrix-cell:hover {
  overflow: visible !important;
  white-space: normal !important;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  border-radius: 4px;
}